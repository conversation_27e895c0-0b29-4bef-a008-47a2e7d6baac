#!/usr/bin/env python3
"""
Script para debugar o fluxo completo e encontrar onde o último dia é perdido
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment

def debug_fluxo_completo():
    """
    Debug do fluxo completo para PETR4
    """
    print("🔍 DEBUGANDO FLUXO COMPLETO - PETR4")
    print("=" * 60)
    
    # Configurar ambiente
    setup_environment()
    
    from classificador_xgboost_sinais import (
        baixar_dados_acao, calcular_features_e_sinais, 
        preparar_dataset, treinar_classificador_binario,
        aplicar_predicoes_modelo_binario
    )
    
    ticker = "PETR4.SA"
    
    # 1. Baixar dados
    print(f"1️⃣ Baixando dados...")
    dados_originais = baixar_dados_acao(ticker, "PETROBRAS", forcar_download=False)
    print(f"   📅 Última data: {dados_originais.index.max().date()}")
    
    # 2. Calcular features
    print(f"\n2️⃣ Calculando features...")
    dados_com_features = calcular_features_e_sinais(dados_originais, ticker, set())
    print(f"   📅 Última data: {dados_com_features.index.max().date()}")
    
    # 3. Preparar dataset (simular com apenas PETR4)
    print(f"\n3️⃣ Preparando dataset...")
    acoes_dados_teste = {ticker: dados_com_features}
    X, y_binary, feature_cols, dataset_completo = preparar_dataset(acoes_dados_teste)
    
    if X is not None:
        print(f"   📊 Total de amostras: {len(X)}")
        # X pode não ter index de data se foi processado
        if hasattr(X, 'index') and len(X) > 0:
            try:
                print(f"   📅 Última data no dataset: {X.index.max().date()}")
            except:
                print(f"   📅 Última data no dataset: {X.index.max()}")
    else:
        print(f"   ❌ Erro ao preparar dataset")
        return
    
    # 4. Treinar modelo (simular)
    print(f"\n4️⃣ Treinando modelo...")
    resultado_tupla = treinar_classificador_binario(X, y_binary, feature_cols, dataset_completo)

    if resultado_tupla is None:
        print(f"   ❌ Erro no treinamento")
        return

    resultados, feature_cols_final = resultado_tupla
    print(f"   ✅ Modelo treinado com sucesso")
    
    # 5. Aplicar predições
    print(f"\n5️⃣ Aplicando predições...")
    acoes_com_predicoes = aplicar_predicoes_modelo_binario(acoes_dados_teste, resultados, feature_cols_final)
    
    if ticker in acoes_com_predicoes:
        dados_finais = acoes_com_predicoes[ticker]
        print(f"   📅 Última data após predições: {dados_finais.index.max().date()}")
        print(f"   📊 Total de dias: {len(dados_finais)}")
        
        # Verificar último dia
        ultimo_dia = dados_finais.iloc[-1]
        print(f"\n   📋 Dados do último dia:")
        print(f"      Data: {ultimo_dia.name.date()}")
        print(f"      Media_OHLC: {ultimo_dia['Media_OHLC']:.2f}")
        print(f"      Pred_Compra: {ultimo_dia.get('Pred_Compra', 'N/A')}")
        print(f"      Pred_Venda: {ultimo_dia.get('Pred_Venda', 'N/A')}")
        print(f"      Prob_Compra: {ultimo_dia.get('Prob_Compra', 'N/A')}")
        print(f"      Prob_Venda: {ultimo_dia.get('Prob_Venda', 'N/A')}")
        
    else:
        print(f"   ❌ PETR4 não encontrada nas predições")
    
    # 6. Simular função de recomendações
    print(f"\n6️⃣ Simulando função de recomendações...")
    
    if ticker in acoes_com_predicoes:
        dados = acoes_com_predicoes[ticker]
        
        # Simular a lógica da função imprimir_recomendacoes_ultimo_dia
        if 'Pred_Venda' in dados.columns:
            coluna_compra = 'Pred_Compra'
            coluna_venda = 'Pred_Venda'
        else:
            coluna_compra = 'Sinal_Compra'
            coluna_venda = 'Sinal_Venda'
        
        print(f"   🔍 Usando colunas: {coluna_compra}, {coluna_venda}")
        
        # Aplicar nossa nova lógica
        if len(dados) > 0:
            ultimo_dia = dados.iloc[-1]
            
            print(f"   📅 Último dia selecionado: {ultimo_dia.name.date()}")
            print(f"   💰 Preço (Media_OHLC): R$ {ultimo_dia['Media_OHLC']:.2f}")
            print(f"   📊 Sinal Compra: {ultimo_dia.get(coluna_compra, 'N/A')}")
            print(f"   📊 Sinal Venda: {ultimo_dia.get(coluna_venda, 'N/A')}")
            
            # Se não tem dados básicos no último dia, usar penúltimo
            if pd.isna(ultimo_dia.get('Media_OHLC', np.nan)):
                print(f"   ⚠️ Media_OHLC é NaN no último dia, usando penúltimo...")
                dados_validos = dados.dropna(subset=['Media_OHLC'])
                if len(dados_validos) > 0:
                    ultimo_dia = dados_validos.iloc[-1]
                    print(f"   📅 Penúltimo dia usado: {ultimo_dia.name.date()}")
        
        print(f"\n   🎯 RESULTADO FINAL:")
        print(f"      Data: {ultimo_dia.name.date()}")
        print(f"      Preço: R$ {ultimo_dia['Media_OHLC']:.2f}")
        print(f"      Compra: {ultimo_dia.get(coluna_compra, 0)}")
        print(f"      Venda: {ultimo_dia.get(coluna_venda, 0)}")

if __name__ == "__main__":
    debug_fluxo_completo()
