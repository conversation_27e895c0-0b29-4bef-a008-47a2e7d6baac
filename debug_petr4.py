#!/usr/bin/env python3
"""
Script para debugar especificamente os dados da PETR4
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment

def verificar_dados_petr4():
    """
    Verifica os dados mais recentes da PETR4
    """
    print("🔍 DEBUGANDO DADOS DA PETR4")
    print("=" * 50)
    
    ticker = "PETR4.SA"
    hoje = datetime.now().date()
    print(f"📅 Data de hoje: {hoje}")
    
    # 1. Verificar dados diretos do yfinance
    print(f"\n1️⃣ Dados diretos do yfinance (últimos 5 dias):")
    try:
        dados_yf = yf.download(ticker, period="5d", progress=False)
        if isinstance(dados_yf.columns, pd.MultiIndex):
            dados_yf.columns = dados_yf.columns.droplevel(1)
        
        print(f"   📊 Total de dias: {len(dados_yf)}")
        if len(dados_yf) > 0:
            print(f"   📅 Primeira data: {dados_yf.index.min().date()}")
            print(f"   📅 Última data: {dados_yf.index.max().date()}")
            print(f"   📈 Últimos 3 dias:")
            for i in range(min(3, len(dados_yf))):
                dia = dados_yf.iloc[-(i+1)]
                data = dia.name.date()
                print(f"      {data}: Open={dia['Open']:.2f}, High={dia['High']:.2f}, Low={dia['Low']:.2f}, Close={dia['Close']:.2f}")
        else:
            print("   ❌ Nenhum dado encontrado")
    except Exception as e:
        print(f"   ❌ Erro: {e}")
    
    # 2. Verificar cache existente
    print(f"\n2️⃣ Verificar cache existente:")
    cache_file = 'data/cache/historical_data/PETR4_historical.csv'
    if os.path.exists(cache_file):
        try:
            dados_cache = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            print(f"   📊 Total de dias no cache: {len(dados_cache)}")
            print(f"   📅 Primeira data no cache: {dados_cache.index.min().date()}")
            print(f"   📅 Última data no cache: {dados_cache.index.max().date()}")
            
            # Verificar últimos dias
            print(f"   📈 Últimos 3 dias no cache:")
            for i in range(min(3, len(dados_cache))):
                dia = dados_cache.iloc[-(i+1)]
                data = dia.name.date()
                print(f"      {data}: Open={dia['Open']:.2f}, High={dia['High']:.2f}, Low={dia['Low']:.2f}, Close={dia['Close']:.2f}")
                
        except Exception as e:
            print(f"   ❌ Erro ao ler cache: {e}")
    else:
        print(f"   📁 Cache não encontrado: {cache_file}")
    
    # 3. Simular o processo do script
    print(f"\n3️⃣ Simulando processo do script:")
    try:
        # Configurar ambiente
        setup_environment()
        
        # Importar função do script
        from classificador_xgboost_sinais import baixar_dados_acao
        
        print(f"   🔄 Baixando dados usando função do script...")
        dados_script = baixar_dados_acao(ticker, "PETROBRAS", forcar_download=False)
        
        if dados_script is not None and len(dados_script) > 0:
            print(f"   📊 Total de dias: {len(dados_script)}")
            print(f"   📅 Primeira data: {dados_script.index.min().date()}")
            print(f"   📅 Última data: {dados_script.index.max().date()}")
            
            # Verificar últimos dias
            print(f"   📈 Últimos 3 dias:")
            for i in range(min(3, len(dados_script))):
                dia = dados_script.iloc[-(i+1)]
                data = dia.name.date()
                print(f"      {data}: Open={dia['Open']:.2f}, High={dia['High']:.2f}, Low={dia['Low']:.2f}, Close={dia['Close']:.2f}")
                
            # Calcular Media_OHLC para o último dia
            ultimo_dia = dados_script.iloc[-1]
            media_ohlc = (ultimo_dia['Open'] + ultimo_dia['High'] + ultimo_dia['Low']) / 3
            print(f"   💰 Media_OHLC último dia: R$ {media_ohlc:.2f}")
            
        else:
            print(f"   ❌ Nenhum dado retornado pela função do script")
            
    except Exception as e:
        print(f"   ❌ Erro na simulação: {e}")
    
    # 4. Verificar se hoje é dia útil
    print(f"\n4️⃣ Verificar se hoje é dia útil:")
    hoje_weekday = hoje.weekday()  # 0=segunda, 6=domingo
    if hoje_weekday < 5:  # Segunda a sexta
        print(f"   ✅ Hoje é dia útil (weekday={hoje_weekday})")
    else:
        print(f"   ⚠️ Hoje é fim de semana (weekday={hoje_weekday})")
        if hoje_weekday == 5:  # Sábado
            print(f"   📅 Último dia útil seria: {hoje - timedelta(days=1)}")
        else:  # Domingo
            print(f"   📅 Último dia útil seria: {hoje - timedelta(days=2)}")

if __name__ == "__main__":
    verificar_dados_petr4()
