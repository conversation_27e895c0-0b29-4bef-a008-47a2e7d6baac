#!/usr/bin/env python3
"""
Sistema de Cache Otimizado para dados históricos do XGBoost
Usa formato Parquet para melhor performance e compressão
"""

import os
import sys
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
import yfinance as yf
from pathlib import Path
import hashlib
import json

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

class OptimizedCache:
    """
    Sistema de cache otimizado usando Parquet
    Inclui cache de features calculadas para acelerar processamento
    """

    def __init__(self):
        self.cache_dir = Path('data/cache/optimized')
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Configurações
        self.compression = 'snappy'  # Compressão rápida e eficiente
        self.metadata_file = self.cache_dir / 'cache_metadata.json'

        # Features que são calculadas e podem ser cacheadas
        self.cacheable_features = [
            # Features básicas
            'Media_OHLC', 'Media_OHLC_PctChange', 'Volatilidade', 'Spread',
            # Features econométricas atuais
            'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA', 'Amihud', 'Roll_Spread',
            'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
            # Features econométricas históricas
            'MFI_Historical', 'Amihud_Historical', 'Roll_Spread_Historical',
            'Hurst_Historical', 'Vol_per_Volume_Historical', 'CMF_Historical', 'AD_Line_Historical',
            # Features temporais
            'Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta',
            'Mes_1', 'Mes_2', 'Mes_3', 'Mes_4', 'Mes_5', 'Mes_6',
            'Mes_7', 'Mes_8', 'Mes_9', 'Mes_10', 'Mes_11', 'Mes_12',
            'Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter',
            'Pre_Feriado_Brasil',
            # Sinais
            'Media_OHLC_Futura', 'Sinal_Compra', 'Sinal_Venda'
        ]

        # Adicionar features lagged dinamicamente baseado na configuração
        self._add_lagged_features()

    def _add_lagged_features(self):
        """
        Adiciona features lagged à lista de features cacheáveis baseado na configuração
        """
        try:
            # Obter configurações de lags
            ohlc_lags = config.get('xgboost.features.ohlc_lags', 5)
            econometric_lags = config.get('xgboost.features.econometric_lags', 3)

            # Adicionar lags da Media_OHLC_PctChange
            for i in range(1, ohlc_lags + 1):
                self.cacheable_features.append(f'Media_OHLC_PctChange_Lag_{i}')

            # Features econométricas que podem ter lags
            econometric_features_all = [
                'Volume', 'Spread', 'Volatilidade',
                'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
            ]

            # Adicionar lags das features econométricas
            for feature in econometric_features_all:
                for i in range(1, econometric_lags + 1):
                    self.cacheable_features.append(f'{feature}_Lag_{i}')

        except Exception as e:
            print(f"     ⚠️ Erro ao configurar features lagged: {e}")

    def _get_cache_path(self, ticker: str) -> Path:
        """Retorna o caminho do arquivo de cache para um ticker"""
        ticker_clean = ticker.replace('.SA', '')
        return self.cache_dir / f'{ticker_clean}.parquet'
    
    def _load_metadata(self) -> dict:
        """Carrega metadados do cache"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_metadata(self, metadata: dict):
        """Salva metadados do cache"""
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
    
    def _calculate_data_hash(self, data: pd.DataFrame) -> str:
        """Calcula hash dos dados para verificar integridade"""
        return hashlib.md5(str(data.values.tobytes()).encode()).hexdigest()
    
    def get_cached_data(self, ticker: str) -> tuple[pd.DataFrame, dict]:
        """
        Recupera dados do cache

        Returns:
            tuple: (dados, info) onde info contém metadados
        """
        cache_path = self._get_cache_path(ticker)
        metadata = self._load_metadata()
        ticker_clean = ticker.replace('.SA', '')

        if not cache_path.exists():
            return None, {}

        try:
            # Ler dados do Parquet
            data = pd.read_parquet(cache_path)
            data.index = pd.to_datetime(data.index)

            # Obter informações do cache
            info = metadata.get(ticker_clean, {})
            info['file_size'] = cache_path.stat().st_size
            info['last_modified'] = datetime.fromtimestamp(cache_path.stat().st_mtime)

            # Verificar se tem features cacheadas
            features_cacheadas = [col for col in self.cacheable_features if col in data.columns]
            info['features_cached'] = len(features_cacheadas)
            info['has_features'] = len(features_cacheadas) > 0

            if info['has_features']:
                print(f"     📁 Cache Parquet encontrado para {ticker} - {len(data)} dias, {len(features_cacheadas)} features ({info['file_size']/1024:.1f}KB)")
            else:
                print(f"     📁 Cache Parquet encontrado para {ticker} - {len(data)} dias, sem features ({info['file_size']/1024:.1f}KB)")

            return data, info

        except Exception as e:
            print(f"     ⚠️ Erro ao ler cache Parquet de {ticker}: {e}")
            return None, {}
    
    def save_cached_data(self, ticker: str, data: pd.DataFrame, source: str = "yfinance"):
        """
        Salva dados no cache

        Args:
            ticker: Código da ação
            data: DataFrame com dados OHLCV e features (se disponíveis)
            source: Fonte dos dados
        """
        cache_path = self._get_cache_path(ticker)
        ticker_clean = ticker.replace('.SA', '')

        try:
            # Preparar dados para salvar
            data_to_save = data.copy()

            # Salvar em formato Parquet com compressão
            data_to_save.to_parquet(
                cache_path,
                compression=self.compression,
                index=True
            )

            # Contar features cacheadas
            features_cacheadas = [col for col in self.cacheable_features if col in data.columns]

            # Atualizar metadados
            metadata = self._load_metadata()
            metadata[ticker_clean] = {
                'ticker': ticker,
                'last_update': datetime.now(),
                'data_start': data.index.min(),
                'data_end': data.index.max(),
                'total_days': len(data),
                'source': source,
                'data_hash': self._calculate_data_hash(data),
                'compression': self.compression,
                'features_cached': len(features_cacheadas),
                'has_features': len(features_cacheadas) > 0
            }
            self._save_metadata(metadata)

            file_size = cache_path.stat().st_size
            if len(features_cacheadas) > 0:
                print(f"     💾 Cache Parquet salvo para {ticker} - {len(data)} dias, {len(features_cacheadas)} features ({file_size/1024:.1f}KB)")
            else:
                print(f"     💾 Cache Parquet salvo para {ticker} - {len(data)} dias ({file_size/1024:.1f}KB)")

        except Exception as e:
            print(f"     ❌ Erro ao salvar cache Parquet de {ticker}: {e}")
    
    def update_cache_with_recent_data_and_features(self, ticker: str, nome: str, tickers_carteira=None) -> pd.DataFrame:
        """
        Atualiza cache com dados recentes E features calculadas (sempre baixa hoje e ontem)
        Se há cache de features, só recalcula features dos últimos 2 dias
        """
        # Tentar carregar cache existente
        cached_data, cache_info = self.get_cached_data(ticker)

        # Determinar período para download
        hoje = datetime.now().date()

        if cached_data is not None and len(cached_data) > 0:
            # Verificar data mais recente no cache
            ultima_data_cache = cached_data.index.max().date()

            # SEMPRE baixar dados do dia atual e anterior
            dias_para_baixar = max(2, (hoje - ultima_data_cache).days + 1)
            data_inicio = hoje - timedelta(days=dias_para_baixar + 1)

            print(f"     🔄 Atualizando cache Parquet de {ticker} - baixando últimos {dias_para_baixar} dias")

            # Baixar dados recentes
            dados_recentes = yf.download(ticker, start=data_inicio, progress=False)

            if dados_recentes is not None and len(dados_recentes) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados_recentes.columns, pd.MultiIndex):
                    dados_recentes.columns = dados_recentes.columns.droplevel(1)

                # Combinar dados históricos com dados recentes
                data_inicio_ts = pd.Timestamp(data_inicio)
                dados_historicos = cached_data[cached_data.index < data_inicio_ts]
                dados_combinados = pd.concat([dados_historicos, dados_recentes])

                # Remover duplicatas e ordenar
                dados_combinados = dados_combinados[~dados_combinados.index.duplicated(keep='last')]
                dados_combinados = dados_combinados.sort_index()

                # Verificar se há features cacheadas
                if cache_info.get('has_features', False):
                    print(f"     ⚡ Features encontradas no cache - recalculando apenas últimos {dias_para_baixar} dias")
                    dados_com_features = self._calcular_features_incrementais(
                        dados_combinados, cached_data, dias_para_baixar, ticker, tickers_carteira
                    )
                else:
                    print(f"     🔧 Sem features no cache - calculando features completas")
                    dados_com_features = self._calcular_features_completas(dados_combinados, ticker, tickers_carteira)

                # Salvar cache atualizado com features
                self.save_cached_data(ticker, dados_com_features)

                nova_ultima_data = dados_com_features.index.max().date()
                print(f"     ✅ Cache Parquet atualizado para {ticker} - última: {nova_ultima_data}")

                return dados_com_features
            else:
                print(f"     ⚠️ Falha ao baixar dados recentes de {ticker}, usando cache existente")
                # Se há features no cache, retornar como está
                if cache_info.get('has_features', False):
                    return cached_data
                else:
                    # Calcular features para dados existentes
                    print(f"     🔧 Calculando features para dados existentes")
                    dados_com_features = self._calcular_features_completas(cached_data, ticker, tickers_carteira)
                    self.save_cached_data(ticker, dados_com_features)
                    return dados_com_features
        else:
            # Não há cache, baixar dados completos e calcular features
            periodo = config.get('xgboost.data_period')
            print(f"     📊 Baixando dados completos de {ticker} ({nome}) - período: {periodo}")

            dados = yf.download(ticker, period=periodo, progress=False)

            if dados is not None and len(dados) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados.columns, pd.MultiIndex):
                    dados.columns = dados.columns.droplevel(1)

                # Calcular features completas
                print(f"     🔧 Calculando features completas para {ticker}")
                dados_com_features = self._calcular_features_completas(dados, ticker, tickers_carteira)

                # Salvar no cache com features
                self.save_cached_data(ticker, dados_com_features)

                return dados_com_features
            else:
                print(f"     ❌ Falha ao baixar dados de {ticker}")
                return None

    def update_cache_with_recent_data(self, ticker: str, nome: str) -> pd.DataFrame:
        """
        Atualiza cache com dados recentes (sempre baixa hoje e ontem)
        """
        # Tentar carregar cache existente
        cached_data, cache_info = self.get_cached_data(ticker)
        
        # Determinar período para download
        hoje = datetime.now().date()
        
        if cached_data is not None and len(cached_data) > 0:
            # Verificar data mais recente no cache
            ultima_data_cache = cached_data.index.max().date()
            
            # SEMPRE baixar dados do dia atual e anterior
            dias_para_baixar = max(2, (hoje - ultima_data_cache).days + 1)
            data_inicio = hoje - timedelta(days=dias_para_baixar + 1)
            
            print(f"     🔄 Atualizando cache Parquet de {ticker} - baixando últimos {dias_para_baixar} dias")
            
            # Baixar dados recentes
            dados_recentes = yf.download(ticker, start=data_inicio, progress=False)
            
            if dados_recentes is not None and len(dados_recentes) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados_recentes.columns, pd.MultiIndex):
                    dados_recentes.columns = dados_recentes.columns.droplevel(1)
                
                # Combinar dados
                data_inicio_ts = pd.Timestamp(data_inicio)
                dados_combinados = cached_data[cached_data.index < data_inicio_ts]
                dados_combinados = pd.concat([dados_combinados, dados_recentes])
                
                # Remover duplicatas e ordenar
                dados_combinados = dados_combinados[~dados_combinados.index.duplicated(keep='last')]
                dados_combinados = dados_combinados.sort_index()
                
                # Salvar cache atualizado
                self.save_cached_data(ticker, dados_combinados)
                
                nova_ultima_data = dados_combinados.index.max().date()
                print(f"     ✅ Cache Parquet atualizado para {ticker} - última: {nova_ultima_data}")
                
                return dados_combinados
            else:
                print(f"     ⚠️ Falha ao baixar dados recentes de {ticker}, usando cache existente")
                return cached_data
        else:
            # Não há cache, baixar dados completos
            periodo = config.get('xgboost.data_period')
            print(f"     📊 Baixando dados completos de {ticker} ({nome}) - período: {periodo}")
            
            dados = yf.download(ticker, period=periodo, progress=False)
            
            if dados is not None and len(dados) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados.columns, pd.MultiIndex):
                    dados.columns = dados.columns.columns.droplevel(1)
                
                # Salvar no cache
                self.save_cached_data(ticker, dados)
                
                return dados
            else:
                print(f"     ❌ Falha ao baixar dados de {ticker}")
                return None
    
    def get_cache_stats(self) -> dict:
        """Retorna estatísticas do cache"""
        metadata = self._load_metadata()
        
        if not metadata:
            return {'total_files': 0, 'total_size': 0}
        
        total_size = 0
        for file_path in self.cache_dir.glob('*.parquet'):
            total_size += file_path.stat().st_size
        
        return {
            'total_files': len(metadata),
            'total_size': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'compression': self.compression,
            'cache_dir': str(self.cache_dir)
        }
    
    def _calcular_features_completas(self, dados: pd.DataFrame, ticker: str, tickers_carteira=None) -> pd.DataFrame:
        """
        Calcula features completas para todos os dados
        """
        try:
            # Importar função de cálculo de features
            from classificador_xgboost_sinais import calcular_features_e_sinais
            return calcular_features_e_sinais(dados, ticker, tickers_carteira)
        except Exception as e:
            print(f"     ⚠️ Erro ao calcular features completas para {ticker}: {e}")
            return dados

    def _calcular_features_incrementais(self, dados_combinados: pd.DataFrame, cached_data: pd.DataFrame,
                                      dias_para_recalcular: int, ticker: str, tickers_carteira=None) -> pd.DataFrame:
        """
        Calcula features apenas para os últimos dias, mantendo features históricas do cache
        """
        try:
            # Importar função de cálculo de features
            from classificador_xgboost_sinais import calcular_features_e_sinais

            # Determinar data de corte para recálculo
            hoje = datetime.now().date()
            data_corte = hoje - timedelta(days=dias_para_recalcular)
            data_corte_ts = pd.Timestamp(data_corte)

            # Separar dados históricos (com features) dos dados recentes (sem features)
            dados_historicos_com_features = cached_data[cached_data.index < data_corte_ts].copy()
            dados_recentes_raw = dados_combinados[dados_combinados.index >= data_corte_ts].copy()

            if len(dados_recentes_raw) == 0:
                # Não há dados novos para processar
                return cached_data

            # Para calcular features dos dados recentes, precisamos de contexto histórico
            # Pegar uma janela maior de dados para cálculos rolling
            janela_contexto = 100  # Dias de contexto para cálculos rolling
            data_contexto = data_corte_ts - timedelta(days=janela_contexto)

            # Dados para cálculo (contexto + recentes)
            dados_para_calculo = dados_combinados[dados_combinados.index >= data_contexto].copy()

            # Calcular features para toda a janela (contexto + recentes)
            dados_com_features_janela = calcular_features_e_sinais(dados_para_calculo, ticker, tickers_carteira)

            # Extrair apenas as features dos dados recentes
            dados_recentes_com_features = dados_com_features_janela[dados_com_features_janela.index >= data_corte_ts]

            # Combinar dados históricos (com features do cache) + dados recentes (com features recalculadas)
            resultado = pd.concat([dados_historicos_com_features, dados_recentes_com_features])

            # Remover duplicatas e ordenar
            resultado = resultado[~resultado.index.duplicated(keep='last')]
            resultado = resultado.sort_index()

            print(f"     ⚡ Features incrementais calculadas - {len(dados_recentes_com_features)} dias recalculados")
            return resultado

        except Exception as e:
            print(f"     ⚠️ Erro no cálculo incremental para {ticker}: {e}")
            print(f"     🔄 Fallback para cálculo completo")
            return self._calcular_features_completas(dados_combinados, ticker, tickers_carteira)

    def clear_cache(self):
        """Remove todos os arquivos de cache"""
        import shutil
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
            print("🗑️ Cache Parquet otimizado limpo")
        else:
            print("📁 Nenhum cache Parquet encontrado para limpar")

# Instância global do cache otimizado
optimized_cache = OptimizedCache()
