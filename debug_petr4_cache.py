#!/usr/bin/env python3
"""
Script para debugar especificamente por que PETR4 não tem dados de hoje
mesmo com a nova estratégia de cache
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment

def debug_petr4_cache():
    """
    Debug específico da PETR4 para entender o problema do cache
    """
    print("🔍 DEBUGANDO PETR4 - PROBLEMA DO CACHE")
    print("=" * 60)
    
    # Configurar ambiente
    setup_environment()
    
    ticker = "PETR4.SA"
    hoje = datetime.now().date()
    ontem = hoje - timedelta(days=1)
    anteontem = hoje - timedelta(days=2)
    
    print(f"📅 Hoje: {hoje}")
    print(f"📅 Ontem: {ontem}")
    print(f"📅 Anteontem: {anteontem}")
    
    # 1. Verificar cache unificado
    print(f"\n1️⃣ Verificando cache unificado:")
    try:
        from cache_unified import CacheUnificado
        cache = CacheUnificado()
        
        # Verificar se PETR4 está no cache unificado
        if hasattr(cache, 'cache_data') and ticker in cache.cache_data:
            dados_cache = cache.cache_data[ticker]
            print(f"   ✅ PETR4 encontrada no cache unificado")
            print(f"   📊 Total de dias: {len(dados_cache)}")
            print(f"   📅 Primeira data: {dados_cache.index.min().date()}")
            print(f"   📅 Última data: {dados_cache.index.max().date()}")
            
            # Verificar últimos 3 dias
            print(f"   📈 Últimos 3 dias no cache unificado:")
            for i in range(min(3, len(dados_cache))):
                dia = dados_cache.iloc[-(i+1)]
                data = dia.name.date()
                dias_atras = (hoje - data).days
                status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                print(f"      {data} ({status}): Close={dia.get('Close', 'N/A'):.2f}")
        else:
            print(f"   ❌ PETR4 NÃO encontrada no cache unificado")
            
    except Exception as e:
        print(f"   ❌ Erro ao verificar cache unificado: {e}")
    
    # 2. Verificar cache individual
    print(f"\n2️⃣ Verificando cache individual:")
    cache_file = 'data/cache/historical_data/PETR4_historical.csv'
    if os.path.exists(cache_file):
        try:
            dados_individual = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            print(f"   ✅ Cache individual encontrado")
            print(f"   📊 Total de dias: {len(dados_individual)}")
            print(f"   📅 Primeira data: {dados_individual.index.min().date()}")
            print(f"   📅 Última data: {dados_individual.index.max().date()}")
            
            # Verificar últimos 3 dias
            print(f"   📈 Últimos 3 dias no cache individual:")
            for i in range(min(3, len(dados_individual))):
                dia = dados_individual.iloc[-(i+1)]
                data = dia.name.date()
                dias_atras = (hoje - data).days
                status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                print(f"      {data} ({status}): Close={dia['Close']:.2f}")
                
        except Exception as e:
            print(f"   ❌ Erro ao ler cache individual: {e}")
    else:
        print(f"   📁 Cache individual não encontrado: {cache_file}")
    
    # 3. Testar download direto
    print(f"\n3️⃣ Testando download direto do Yahoo Finance:")
    try:
        import yfinance as yf
        
        # Baixar últimos 3 dias
        inicio = hoje - timedelta(days=3)
        dados_yf = yf.download(ticker, start=inicio, progress=False)
        if isinstance(dados_yf.columns, pd.MultiIndex):
            dados_yf.columns = dados_yf.columns.droplevel(1)
        
        print(f"   📊 Dados baixados: {len(dados_yf)} dias")
        if len(dados_yf) > 0:
            print(f"   📅 Última data disponível: {dados_yf.index.max().date()}")
            
            # Verificar se tem dados de hoje
            dados_hoje = dados_yf[dados_yf.index.date == hoje]
            if len(dados_hoje) > 0:
                print(f"   ✅ Yahoo Finance TEM dados de HOJE!")
                dia_hoje = dados_hoje.iloc[0]
                print(f"      {hoje}: Open={dia_hoje['Open']:.2f}, Close={dia_hoje['Close']:.2f}")
            else:
                print(f"   ❌ Yahoo Finance NÃO tem dados de hoje")
            
            # Mostrar todos os dados disponíveis
            print(f"   📈 Todos os dados disponíveis:")
            for idx, row in dados_yf.iterrows():
                data = idx.date()
                dias_atras = (hoje - data).days
                status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                print(f"      {data} ({status}): Open={row['Open']:.2f}, Close={row['Close']:.2f}")
        else:
            print(f"   ❌ Nenhum dado baixado")
            
    except Exception as e:
        print(f"   ❌ Erro no download: {e}")
    
    # 4. Simular o processo do script
    print(f"\n4️⃣ Simulando processo do script:")
    try:
        from classificador_xgboost_sinais import baixar_dados_acao
        
        print(f"   🔄 Baixando dados usando função do script...")
        dados_script = baixar_dados_acao(ticker, "PETROBRAS", forcar_download=False)
        
        if dados_script is not None and len(dados_script) > 0:
            print(f"   📊 Total de dias: {len(dados_script)}")
            print(f"   📅 Última data: {dados_script.index.max().date()}")
            
            # Verificar se tem dados de hoje
            ultima_data = dados_script.index.max().date()
            if ultima_data == hoje:
                print(f"   ✅ Script retornou dados de HOJE!")
            elif ultima_data == ontem:
                print(f"   ⚠️ Script retornou dados de ONTEM")
            else:
                dias_atras = (hoje - ultima_data).days
                print(f"   ❌ Script retornou dados de {dias_atras} dias atrás")
            
            # Verificar últimos 3 dias
            print(f"   📈 Últimos 3 dias retornados pelo script:")
            for i in range(min(3, len(dados_script))):
                dia = dados_script.iloc[-(i+1)]
                data = dia.name.date()
                dias_atras = (hoje - data).days
                status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                print(f"      {data} ({status}): Close={dia['Close']:.2f}")
                
        else:
            print(f"   ❌ Script não retornou dados")
            
    except Exception as e:
        print(f"   ❌ Erro na simulação: {e}")
    
    # 5. Verificar se o problema está na consolidação
    print(f"\n5️⃣ Verificando processo de consolidação:")
    try:
        from cache_unified import CacheUnificado
        cache = CacheUnificado()
        
        print(f"   🔄 Forçando consolidação do cache...")
        cache.consolidar_cache()
        
        # Verificar novamente após consolidação
        if hasattr(cache, 'cache_data') and ticker in cache.cache_data:
            dados_pos_consolidacao = cache.cache_data[ticker]
            ultima_data_pos = dados_pos_consolidacao.index.max().date()
            print(f"   📅 Última data após consolidação: {ultima_data_pos}")
            
            if ultima_data_pos == hoje:
                print(f"   ✅ SUCESSO: Após consolidação tem dados de HOJE!")
            else:
                dias_atras = (hoje - ultima_data_pos).days
                print(f"   ❌ Ainda sem dados de hoje ({dias_atras} dias atrás)")
        else:
            print(f"   ❌ PETR4 ainda não encontrada após consolidação")
            
    except Exception as e:
        print(f"   ❌ Erro na consolidação: {e}")
    
    # 6. Conclusão
    print(f"\n6️⃣ DIAGNÓSTICO:")
    print(f"   🎯 Possíveis causas do problema:")
    print(f"      • Cache unificado não está sendo atualizado corretamente")
    print(f"      • Processo de consolidação não está funcionando")
    print(f"      • Dados de hoje não estão sendo baixados pelo Yahoo Finance")
    print(f"      • Problema na lógica de merge dos dados")
    print(f"   💡 Próximos passos: Verificar logs do cache unificado")

if __name__ == "__main__":
    debug_petr4_cache()
