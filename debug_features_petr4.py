#!/usr/bin/env python3
"""
Script para debugar features da PETR4 que podem estar causando NaN
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment
from classificador_xgboost_sinais import baixar_dados_acao, calcular_features_e_sinais

def debug_features_petr4():
    """
    Debug das features da PETR4 para encontrar NaN no último dia
    """
    print("🔍 DEBUGANDO FEATURES DA PETR4")
    print("=" * 50)
    
    # Configurar ambiente
    setup_environment()
    
    ticker = "PETR4.SA"
    
    # 1. Baixar dados
    print(f"1️⃣ Baixando dados da {ticker}...")
    dados = baixar_dados_acao(ticker, "PETROBRAS", forcar_download=False)
    
    if dados is None:
        print("❌ Erro ao baixar dados")
        return
    
    print(f"   📊 Total de dias: {len(dados)}")
    print(f"   📅 Última data: {dados.index.max().date()}")
    
    # 2. Calcular features
    print(f"\n2️⃣ Calculando features...")
    dados_com_features = calcular_features_e_sinais(dados, ticker, set())
    
    print(f"   📊 Total de dias após features: {len(dados_com_features)}")
    print(f"   📅 Última data após features: {dados_com_features.index.max().date()}")
    
    # 3. Verificar NaN no último dia
    print(f"\n3️⃣ Verificando NaN no último dia...")
    ultimo_dia = dados_com_features.iloc[-1]
    
    # Verificar todas as colunas
    colunas_com_nan = []
    for col in dados_com_features.columns:
        if pd.isna(ultimo_dia[col]):
            colunas_com_nan.append(col)
    
    if colunas_com_nan:
        print(f"   ⚠️ Colunas com NaN no último dia ({len(colunas_com_nan)}):")
        for col in colunas_com_nan:
            print(f"      - {col}")
    else:
        print(f"   ✅ Nenhuma coluna com NaN no último dia")
    
    # 4. Verificar features específicas importantes
    print(f"\n4️⃣ Verificando features importantes...")
    features_importantes = [
        'Media_OHLC', 'Media_OHLC_PctChange', 'Volume', 'Spread', 'Volatilidade',
        'Sinal_Compra', 'Sinal_Venda', 'Media_OHLC_Futura'
    ]
    
    for feature in features_importantes:
        if feature in dados_com_features.columns:
            valor = ultimo_dia[feature]
            status = "❌ NaN" if pd.isna(valor) else f"✅ {valor}"
            print(f"   {feature}: {status}")
        else:
            print(f"   {feature}: ❌ Não existe")
    
    # 5. Verificar penúltimo dia para comparação
    print(f"\n5️⃣ Comparando com penúltimo dia...")
    if len(dados_com_features) >= 2:
        penultimo_dia = dados_com_features.iloc[-2]
        print(f"   📅 Data penúltimo dia: {penultimo_dia.name.date()}")
        
        for feature in features_importantes:
            if feature in dados_com_features.columns:
                valor_ultimo = ultimo_dia[feature]
                valor_penultimo = penultimo_dia[feature]
                
                print(f"   {feature}:")
                print(f"      Penúltimo: {valor_penultimo if not pd.isna(valor_penultimo) else 'NaN'}")
                print(f"      Último: {valor_ultimo if not pd.isna(valor_ultimo) else 'NaN'}")
    
    # 6. Simular dropna() das features
    print(f"\n6️⃣ Simulando dropna() das features...")
    
    # Simular as feature_cols que seriam usadas
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')
    
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + ['Volume', 'Spread', 'Volatilidade'] + weekday_features + month_features + quarter_features + holiday_features
    
    econometric_features_current = [
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
    ]
    
    feature_cols = basic_features + econometric_features_current
    
    # Verificar quais features existem
    features_existentes = [col for col in feature_cols if col in dados_com_features.columns]
    features_faltando = [col for col in feature_cols if col not in dados_com_features.columns]
    
    print(f"   📊 Features existentes: {len(features_existentes)}/{len(feature_cols)}")
    if features_faltando:
        print(f"   ⚠️ Features faltando: {features_faltando}")
    
    # Aplicar dropna() nas features
    X = dados_com_features[features_existentes].dropna()
    
    print(f"   📊 Dados antes do dropna(): {len(dados_com_features)}")
    print(f"   📊 Dados após dropna(): {len(X)}")
    print(f"   📅 Última data antes: {dados_com_features.index.max().date()}")
    print(f"   📅 Última data após: {X.index.max().date()}")
    
    if len(X) < len(dados_com_features):
        linhas_removidas = len(dados_com_features) - len(X)
        print(f"   ⚠️ {linhas_removidas} linha(s) removida(s) pelo dropna()")
        
        # Identificar quais linhas foram removidas
        indices_removidos = dados_com_features.index.difference(X.index)
        print(f"   📅 Datas removidas: {[idx.date() for idx in indices_removidos]}")

if __name__ == "__main__":
    debug_features_petr4()
