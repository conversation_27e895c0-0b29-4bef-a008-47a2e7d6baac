#!/usr/bin/env python3
"""
Script para debugar especificamente por que VALE3 não tem dados de hoje (18/07)
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment

def debug_vale3_hoje():
    """
    Debug específico da VALE3 para entender por que não tem dados de hoje
    """
    print("🔍 DEBUGANDO VALE3 - POR QUE NÃO TEM DADOS DE HOJE?")
    print("=" * 70)
    
    ticker = "VALE3.SA"
    hoje = datetime.now().date()
    print(f"📅 Data de hoje: {hoje} ({hoje.strftime('%A')})")
    
    # 1. Verificar dados diretos do yfinance HOJE
    print(f"\n1️⃣ Testando download direto do yfinance para HOJE:")
    try:
        # Tentar baixar apenas hoje
        dados_hoje = yf.download(ticker, start=hoje, end=hoje + timedelta(days=1), progress=False)
        if isinstance(dados_hoje.columns, pd.MultiIndex):
            dados_hoje.columns = dados_hoje.columns.droplevel(1)
        
        print(f"   📊 Dados de hoje ({hoje}): {len(dados_hoje)} registros")
        if len(dados_hoje) > 0:
            print(f"   ✅ VALE3 TEM dados de hoje!")
            for idx, row in dados_hoje.iterrows():
                data = idx.date()
                print(f"      {data}: Open={row['Open']:.2f}, High={row['High']:.2f}, Low={row['Low']:.2f}, Close={row['Close']:.2f}, Volume={row['Volume']:,.0f}")
        else:
            print(f"   ❌ VALE3 NÃO tem dados de hoje")
            
    except Exception as e:
        print(f"   ❌ Erro no download de hoje: {e}")
    
    # 2. Verificar dados dos últimos 5 dias
    print(f"\n2️⃣ Verificando últimos 5 dias:")
    try:
        dados_5dias = yf.download(ticker, period="5d", progress=False)
        if isinstance(dados_5dias.columns, pd.MultiIndex):
            dados_5dias.columns = dados_5dias.columns.droplevel(1)
        
        print(f"   📊 Total de dias nos últimos 5 dias: {len(dados_5dias)}")
        if len(dados_5dias) > 0:
            print(f"   📅 Primeira data: {dados_5dias.index.min().date()}")
            print(f"   📅 Última data: {dados_5dias.index.max().date()}")
            print(f"   📈 Últimos dados disponíveis:")
            for i in range(min(5, len(dados_5dias))):
                dia = dados_5dias.iloc[-(i+1)]
                data = dia.name.date()
                dias_atras = (hoje - data).days
                status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                print(f"      {data} ({status}): Open={dia['Open']:.2f}, High={dia['High']:.2f}, Low={dia['Low']:.2f}, Close={dia['Close']:.2f}, Volume={dia['Volume']:,.0f}")
        else:
            print(f"   ❌ Nenhum dado encontrado")
    except Exception as e:
        print(f"   ❌ Erro: {e}")
    
    # 3. Verificar se hoje é dia útil
    print(f"\n3️⃣ Verificar se hoje é dia útil:")
    hoje_weekday = hoje.weekday()  # 0=segunda, 6=domingo
    dias_semana = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo']
    print(f"   📅 Hoje é: {dias_semana[hoje_weekday]} (weekday={hoje_weekday})")
    
    if hoje_weekday < 5:  # Segunda a sexta
        print(f"   ✅ Hoje É dia útil - deveria ter dados")
    else:
        print(f"   ⚠️ Hoje NÃO é dia útil")
        if hoje_weekday == 5:  # Sábado
            ultimo_util = hoje - timedelta(days=1)  # Sexta
        else:  # Domingo
            ultimo_util = hoje - timedelta(days=2)  # Sexta
        print(f"   📅 Último dia útil seria: {ultimo_util} ({dias_semana[ultimo_util.weekday()]})")
    
    # 4. Verificar horário atual
    print(f"\n4️⃣ Verificar horário atual:")
    agora = datetime.now()
    print(f"   🕐 Horário atual: {agora.strftime('%H:%M:%S')}")
    
    # Horário da bolsa brasileira (aproximado)
    abertura = agora.replace(hour=10, minute=0, second=0, microsecond=0)
    fechamento = agora.replace(hour=17, minute=0, second=0, microsecond=0)
    
    if agora < abertura:
        print(f"   ⏰ Antes da abertura da bolsa (10:00)")
        print(f"   💡 Dados de hoje podem não estar disponíveis ainda")
    elif agora > fechamento:
        print(f"   ⏰ Após o fechamento da bolsa (17:00)")
        print(f"   💡 Dados de hoje deveriam estar disponíveis")
    else:
        print(f"   ⏰ Durante o pregão (10:00 - 17:00)")
        print(f"   💡 Dados de hoje podem estar sendo atualizados")
    
    # 5. Verificar cache atual
    print(f"\n5️⃣ Verificar cache atual da VALE3:")
    cache_file = 'data/cache/historical_data/VALE3_historical.csv'
    if os.path.exists(cache_file):
        try:
            dados_cache = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            print(f"   📊 Total de dias no cache: {len(dados_cache)}")
            print(f"   📅 Primeira data no cache: {dados_cache.index.min().date()}")
            print(f"   📅 Última data no cache: {dados_cache.index.max().date()}")
            
            ultima_data_cache = dados_cache.index.max().date()
            dias_diferenca = (hoje - ultima_data_cache).days
            
            if dias_diferenca == 0:
                print(f"   ✅ Cache tem dados de HOJE")
            elif dias_diferenca == 1:
                print(f"   ⚠️ Cache tem dados de ONTEM (1 dia atrás)")
            else:
                print(f"   ❌ Cache desatualizado ({dias_diferenca} dias atrás)")
                
            # Mostrar últimos 3 dias do cache
            print(f"   📈 Últimos 3 dias no cache:")
            for i in range(min(3, len(dados_cache))):
                dia = dados_cache.iloc[-(i+1)]
                data = dia.name.date()
                print(f"      {data}: Open={dia['Open']:.2f}, High={dia['High']:.2f}, Low={dia['Low']:.2f}, Close={dia['Close']:.2f}")
                
        except Exception as e:
            print(f"   ❌ Erro ao ler cache: {e}")
    else:
        print(f"   📁 Cache não encontrado: {cache_file}")
    
    # 6. Testar download forçado
    print(f"\n6️⃣ Testando download forçado (últimos 3 dias):")
    try:
        inicio = hoje - timedelta(days=3)
        dados_forcado = yf.download(ticker, start=inicio, progress=False)
        if isinstance(dados_forcado.columns, pd.MultiIndex):
            dados_forcado.columns = dados_forcado.columns.droplevel(1)
        
        print(f"   📊 Dados baixados: {len(dados_forcado)} dias")
        if len(dados_forcado) > 0:
            print(f"   📅 Última data baixada: {dados_forcado.index.max().date()}")
            
            # Verificar se tem dados de hoje
            dados_hoje_forcado = dados_forcado[dados_forcado.index.date == hoje]
            if len(dados_hoje_forcado) > 0:
                print(f"   ✅ DOWNLOAD FORÇADO encontrou dados de HOJE!")
                dia_hoje = dados_hoje_forcado.iloc[0]
                print(f"      {hoje}: Open={dia_hoje['Open']:.2f}, High={dia_hoje['High']:.2f}, Low={dia_hoje['Low']:.2f}, Close={dia_hoje['Close']:.2f}")
            else:
                print(f"   ❌ DOWNLOAD FORÇADO NÃO encontrou dados de hoje")
                print(f"   📈 Dados mais recentes:")
                for i in range(min(3, len(dados_forcado))):
                    dia = dados_forcado.iloc[-(i+1)]
                    data = dia.name.date()
                    dias_atras = (hoje - data).days
                    print(f"      {data} ({dias_atras} dias atrás): Close={dia['Close']:.2f}")
        else:
            print(f"   ❌ Nenhum dado baixado")
            
    except Exception as e:
        print(f"   ❌ Erro no download forçado: {e}")
    
    # 7. Conclusão
    print(f"\n7️⃣ CONCLUSÃO:")
    print(f"   🎯 Se VALE3 não tem dados de hoje, pode ser por:")
    print(f"      • Hoje não é dia útil (fim de semana/feriado)")
    print(f"      • Dados ainda não foram disponibilizados pelo Yahoo Finance")
    print(f"      • Problema temporário na fonte de dados")
    print(f"      • Ação suspensa ou com problemas específicos")
    print(f"   💡 O sistema usa o último dia disponível, que é o comportamento correto")

if __name__ == "__main__":
    debug_vale3_hoje()
