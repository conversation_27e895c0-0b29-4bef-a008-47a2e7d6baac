#!/usr/bin/env python3
"""
Script para forçar atualização do cache das ações com dados desatualizados
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Adicionar o diretório src ao path
sys.path.append('src')
from config_loader import config, setup_environment

def fix_cache_acoes_desatualizadas():
    """
    Força atualização do cache para ações com dados desatualizados
    """
    print("🔧 CORRIGINDO CACHE DE AÇÕES DESATUALIZADAS")
    print("=" * 60)
    
    # Configurar ambiente
    setup_environment()
    
    from cache_unified import CacheUnificado
    
    # Ações que estavam com dados desatualizados
    acoes_problematicas = [
        ("PETR4.SA", "PETROBRAS"),
        ("VALE3.SA", "VALE"),
        ("BBAS3.SA", "BANCO DO BRASIL"),
        ("STBP3.SA", "SANTOS BRASIL")
    ]
    
    print(f"🎯 Ações a serem corrigidas: {len(acoes_problematicas)}")
    for ticker, nome in acoes_problematicas:
        print(f"   - {ticker} ({nome})")
    
    # Inicializar cache unificado
    cache = CacheUnificado()
    
    # Forçar download das ações problemáticas
    print(f"\n🔄 Forçando download das ações problemáticas...")
    
    for ticker, nome in acoes_problematicas:
        print(f"\n📊 Processando {ticker} ({nome})...")
        
        # Verificar data atual no cache
        try:
            dados_cache = cache.obter_dados_acao(ticker)
            if dados_cache is not None and len(dados_cache) > 0:
                ultima_data_cache = dados_cache.index.max().date()
                hoje = datetime.now().date()
                dias_diferenca = (hoje - ultima_data_cache).days
                print(f"   📅 Última data no cache: {ultima_data_cache} ({dias_diferenca} dias atrás)")
            else:
                print(f"   📅 Nenhum dado no cache")
        except Exception as e:
            print(f"   ⚠️ Erro ao verificar cache: {e}")
        
        # Forçar download completo
        try:
            print(f"   🔄 Forçando download completo...")
            dados_novos = cache.baixar_dados_acao_individual(
                ticker, 
                nome, 
                forcar_download=True,
                calcular_features=True
            )
            
            if dados_novos is not None and len(dados_novos) > 0:
                ultima_data_nova = dados_novos.index.max().date()
                print(f"   ✅ Download concluído: {len(dados_novos)} dias, última data: {ultima_data_nova}")
                
                # Verificar se agora tem dados de hoje
                hoje = datetime.now().date()
                if ultima_data_nova == hoje:
                    print(f"   🎯 SUCESSO: Dados atualizados para hoje!")
                elif (hoje - ultima_data_nova).days <= 1:
                    print(f"   ✅ OK: Dados atualizados (diferença: {(hoje - ultima_data_nova).days} dia(s))")
                else:
                    print(f"   ⚠️ Ainda desatualizado: {(hoje - ultima_data_nova).days} dias de diferença")
            else:
                print(f"   ❌ Falha no download")
                
        except Exception as e:
            print(f"   ❌ Erro no download: {e}")
    
    # Consolidar cache
    print(f"\n💾 Consolidando cache unificado...")
    try:
        cache.consolidar_cache()
        print(f"   ✅ Cache consolidado com sucesso")
    except Exception as e:
        print(f"   ❌ Erro na consolidação: {e}")
    
    # Verificação final
    print(f"\n🔍 VERIFICAÇÃO FINAL:")
    for ticker, nome in acoes_problematicas:
        try:
            dados_finais = cache.obter_dados_acao(ticker)
            if dados_finais is not None and len(dados_finais) > 0:
                ultima_data_final = dados_finais.index.max().date()
                hoje = datetime.now().date()
                dias_diferenca = (hoje - ultima_data_final).days
                
                if dias_diferenca == 0:
                    status = "✅ HOJE"
                elif dias_diferenca == 1:
                    status = "✅ ONTEM"
                elif dias_diferenca <= 3:
                    status = f"⚠️ {dias_diferenca} DIAS ATRÁS"
                else:
                    status = f"❌ {dias_diferenca} DIAS ATRÁS"
                
                print(f"   {ticker}: {ultima_data_final} ({status})")
            else:
                print(f"   {ticker}: ❌ SEM DADOS")
        except Exception as e:
            print(f"   {ticker}: ❌ ERRO: {e}")

if __name__ == "__main__":
    fix_cache_acoes_desatualizadas()
