#!/usr/bin/env python3
"""
Teste do download em lote para verificar se está baixando dados de hoje
"""

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta

def test_batch_download():
    """
    Testa se o download em lote está baixando dados de hoje
    """
    print("🔍 TESTANDO DOWNLOAD EM LOTE")
    print("=" * 50)
    
    # Configurar datas
    hoje = datetime.now().date()
    ontem = hoje - timedelta(days=1)
    anteontem = hoje - timedelta(days=2)
    data_inicio = anteontem
    
    print(f"📅 Hoje: {hoje}")
    print(f"📅 Ontem: {ontem}")
    print(f"📅 Anteontem: {anteontem}")
    print(f"📅 Data início download: {data_inicio}")
    
    # Testar com PETR4 e VALE3
    tickers = ['PETR4.SA', 'VALE3.SA']
    tickers_str = ' '.join(tickers)
    
    print(f"\n🔄 Baixando dados em lote para: {tickers_str}")
    print(f"   Start: {data_inicio}")
    
    try:
        # Download em lote
        dados_lote = yf.download(tickers_str, start=data_inicio, progress=False, group_by='ticker')
        
        print(f"\n📊 Resultado do download:")
        print(f"   Tipo: {type(dados_lote)}")
        print(f"   Shape: {dados_lote.shape if hasattr(dados_lote, 'shape') else 'N/A'}")
        
        if hasattr(dados_lote, 'columns'):
            print(f"   Colunas: {dados_lote.columns.names}")
            if hasattr(dados_lote.columns, 'levels'):
                print(f"   Tickers: {list(dados_lote.columns.levels[0])}")
        
        # Verificar dados por ticker
        for ticker in tickers:
            print(f"\n📈 {ticker}:")
            try:
                if len(tickers) == 1:
                    ticker_data = dados_lote
                else:
                    ticker_data = dados_lote[ticker]
                
                print(f"   Shape: {ticker_data.shape}")
                print(f"   Primeira data: {ticker_data.index.min().date()}")
                print(f"   Última data: {ticker_data.index.max().date()}")
                
                # Verificar se tem dados de hoje
                dados_hoje = ticker_data[ticker_data.index.date == hoje]
                if len(dados_hoje) > 0:
                    print(f"   ✅ TEM dados de HOJE!")
                    print(f"      Close hoje: {dados_hoje['Close'].iloc[0]:.2f}")
                else:
                    print(f"   ❌ NÃO tem dados de hoje")
                
                # Mostrar últimos 3 dias
                print(f"   📅 Últimos 3 dias:")
                for i in range(min(3, len(ticker_data))):
                    dia = ticker_data.iloc[-(i+1)]
                    data = dia.name.date()
                    dias_atras = (hoje - data).days
                    status = "HOJE" if dias_atras == 0 else f"{dias_atras} dia(s) atrás"
                    print(f"      {data} ({status}): Close={dia['Close']:.2f}")
                    
            except Exception as e:
                print(f"   ❌ Erro ao processar {ticker}: {e}")
        
    except Exception as e:
        print(f"❌ Erro no download: {e}")

if __name__ == "__main__":
    test_batch_download()
